<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Ảnh Bài Báo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .image-preview {
            max-width: 300px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Test Upload Ảnh Bài Báo</h1>
    
    <!-- Form upload ảnh -->
    <div>
        <h2>1. Upload Ảnh</h2>
        <form id="uploadForm">
            <div class="form-group">
                <label for="imageFile">Chọn ảnh:</label>
                <input type="file" id="imageFile" name="image" accept="image/*" required>
            </div>
            <button type="submit">Upload Ảnh</button>
        </form>
        <div id="uploadResult"></div>
    </div>

    <!-- Form tạo bài báo -->
    <div>
        <h2>2. Tạo Bài Báo</h2>
        <form id="newsForm">
            <div class="form-group">
                <label for="title">Tiêu đề:</label>
                <input type="text" id="title" name="title" required>
            </div>
            <div class="form-group">
                <label for="author">Tác giả:</label>
                <input type="text" id="author" name="author" required>
            </div>
            <div class="form-group">
                <label for="contentHtml">Nội dung HTML:</label>
                <textarea id="contentHtml" name="contentHtml" required></textarea>
            </div>
            <div class="form-group">
                <label for="thumbnail">URL Ảnh đại diện:</label>
                <input type="text" id="thumbnail" name="thumbnail" placeholder="Sẽ được điền tự động sau khi upload ảnh">
            </div>
            <div class="form-group">
                <label for="status">Trạng thái:</label>
                <select id="status" name="status">
                    <option value="draft">Bản nháp</option>
                    <option value="published">Đã xuất bản</option>
                </select>
            </div>
            <div class="form-group">
                <label for="tags">Tags (cách nhau bằng dấu phẩy):</label>
                <input type="text" id="tags" name="tags" placeholder="tag1, tag2, tag3">
            </div>
            <button type="submit">Tạo Bài Báo</button>
        </form>
        <div id="newsResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        // Handle upload ảnh
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('imageFile');
            formData.append('image', fileInput.files[0]);

            try {
                const response = await fetch(`${API_BASE}/news/upload-image`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                const resultDiv = document.getElementById('uploadResult');

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <p><strong>Upload thành công!</strong></p>
                            <p>URL: ${result.data.url}</p>
                            <p>Tên file: ${result.data.filename}</p>
                            <p>Kích thước: ${(result.data.size / 1024).toFixed(2)} KB</p>
                            <img src="${API_BASE}${result.data.url}" alt="Uploaded image" class="image-preview">
                        </div>
                    `;
                    
                    // Tự động điền URL vào form tạo bài báo
                    document.getElementById('thumbnail').value = `${API_BASE}${result.data.url}`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <p><strong>Lỗi:</strong> ${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('uploadResult').innerHTML = `
                    <div class="result error">
                        <p><strong>Lỗi:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });

        // Handle tạo bài báo
        document.getElementById('newsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // Chuyển tags thành array
            if (data.tags) {
                data.tags = data.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
            }

            try {
                const response = await fetch(`${API_BASE}/news`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('newsResult');

                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <p><strong>Tạo bài báo thành công!</strong></p>
                            <p>ID: ${result.data._id}</p>
                            <p>Tiêu đề: ${result.data.title}</p>
                            <p>Tác giả: ${result.data.author}</p>
                            <p>Trạng thái: ${result.data.status}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <p><strong>Lỗi:</strong> ${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('newsResult').innerHTML = `
                    <div class="result error">
                        <p><strong>Lỗi:</strong> ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
