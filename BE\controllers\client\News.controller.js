const News = require("../../model/News.model")
const multer = require('multer')
const path = require('path')
const fs = require('fs')


const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '../../uploads/news')
        // Tạo thư mục nếu chưa tồn tại
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true })
        }
        cb(null, uploadPath)
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
        cb(null, 'news-' + uniqueSuffix + path.extname(file.originalname))
    }
})

// Filter để chỉ cho phép ảnh
const fileFilter = (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
        cb(null, true)
    } else {
        cb(new Error('Chỉ cho phép upload file ảnh!'), false)
    }
}

const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024 // Giới hạn 5MB
    }
})

module.exports.uploadImage = upload.single('image')

module.exports.index = async (req, res) => {
    console.log("ok");
    const report = await News.find({}).sort({ createdAt: -1 });
    res.json({
        success: true,
        data: report
    })
}

// API để upload ảnh riêng biệt
module.exports.uploadNewsImage = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'Không có file ảnh được upload'
            })
        }

        // Tạo URL để truy cập ảnh
        const imageUrl = `/uploads/news/${req.file.filename}`

        res.json({
            success: true,
            message: 'Upload ảnh thành công',
            data: {
                filename: req.file.filename,
                originalName: req.file.originalname,
                size: req.file.size,
                url: imageUrl
            }
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Lỗi khi upload ảnh',
            error: error.message
        })
    }
}

module.exports.postNews = async (req, res) => {
    try {
        const { title, contentHtml, author, thumbnail, status, tags } = req.body;

        // Validate required fields
        if (!title || !contentHtml || !author) {
            return res.status(400).json({
                success: false,
                message: 'Thiếu thông tin bắt buộc: title, contentHtml, author'
            })
        }

        const newNews = new News({
            title,
            contentHtml,
            author,
            thumbnail, // URL ảnh đã được upload trước đó
            status: status || 'draft',
            tags: tags || []
        });

        await newNews.save();

        res.json({
            success: true,
            message: 'Tạo bài báo thành công',
            data: newNews
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Lỗi khi tạo bài báo',
            error: error.message
        })
    }
}